{
    "version": "0.2.0",
    "configurations": [
        {
            // JKOPay BatchSystem API 專案 Debug 設定
            "name": "JKOPay.BatchSystem.Api",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/JKOPay.BatchSystem.Api/bin/Debug/net9.0/JKOPay.BatchSystem.Api.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/JKOPay.BatchSystem.Api",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "local",
                "ASPNETCORE_URLS": "http://localhost:5022;https://localhost:5001"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "console": "internalConsole"
        },
        {
            // JKOPay BatchSystem Operator 專案 Debug 設定
            "name": "JKOPay.BatchSystem.Operator",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/JKOPay.BatchSystem.Operator/bin/Debug/net9.0/JKOPay.BatchSystem.Operator.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/JKOPay.BatchSystem.Operator",
            "console": "internalConsole",
            "stopAtEntry": false,
            "env": {
                "ASPNETCORE_ENVIRONMENT": "local"
            }
        },
        {
            // 附加到執行中的 .NET 程序
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        }
    ]
}
