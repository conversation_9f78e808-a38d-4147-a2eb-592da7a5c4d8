apiVersion: v1
kind: Service
metadata:
  labels:
    app: aprd-mysql
    tier: backend
  name: aprd-mysql
  namespace: foundation
spec:
  type: NodePort
  ports:
    - name: "mysql"
      port: 3306
      targetPort: 3306
      nodePort: 30304
  selector:
    hktv: aprd-mysql
status:
  loadBalancer: {}

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: aprd-mysql
    tier: backend
  name: aprd-mysql
  namespace: foundation
spec:
  replicas: 1
  selector:
    matchLabels:
      hktv: aprd-mysql
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        hktv: aprd-mysql
    spec:
      containers:
        - args:
            - --innodb_use_native_aio=0
            - --innodb_buffer_pool_size=64M
            - --default-time-zone=+08:00
            - --lower_case_table_names=1
            - --character-set-server=utf8mb4
            - --collation-server=utf8mb4_unicode_ci
          env:
            - name: MYSQL_ROOT_PASSWORD
              value: root
            - name: MYSQL_DATABASE
              value: batchsystem
            - name: MYSQL_USER
              value: batchuser
            - name: MYSQL_PASSWORD
              value: batchpw
          image: mysql:8.3.0
          name: aprd-mysql
          ports:
            - containerPort: 3306
          resources:
            requests:
              cpu: 200m
              memory: 512Mi
            limits:
              cpu: 500m
              memory: 1Gi
          volumeMounts:
            - name: mysql-volume
              mountPath: /var/lib/mysql
      restartPolicy: Always
      volumes:
        - name: mysql-volume
          hostPath:
            #mac
            path: "/var/lib/docker/volumes/mysql/_data"