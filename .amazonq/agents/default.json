{"name": "default-agent", "version": "1.0.0", "description": "Default agent configuration", "mcpServers": {}, "tools": ["fsRead", "fsWrite", "fsReplace", "listDirectory", "fileSearch", "executeBash", "codeReview"], "allowedTools": ["fsRead", "listDirectory", "fileSearch", "codeReview"], "toolsSettings": {"execute_bash": {"alwaysAllow": [{"preset": "readOnly"}]}, "use_aws": {"alwaysAllow": [{"preset": "readOnly"}]}}, "includedFiles": ["AmazonQ.md", "README.md", ".amazonq/rules/**/*.md"], "resources": [], "promptHooks": []}