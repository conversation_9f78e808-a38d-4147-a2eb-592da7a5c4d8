{{
    $global.Host = `${Host}`;
    console.info('Host: ' + $global.Host);
}}

GET {{$global.Host}}/v1/schedule/list/foundation/adminpanel?currentPage=1&pageSize=100 HTTP/1.1

###
POST {{$global.Host}}/v1/schedule/update HTTP/1.1
Content-Type: application/json

{
  "JobId": 0,
  "CronWorkflowId": "f746950c-f3e2-465b-a2a3-91fdd5894ba9",
  "ScheduleJobName": "dev-foundation-test-sj",
  "LatestStatus": "none",
  "ExecutionCycle": "* */20 * * *",
  "WorkflowTemplateRef": "dev-foundation-batchsystem-main-cron-workflow-template",
  "LatestWorkflowName": null,
  "LatestWorkflowId": null,
  "LatestStartedAt": null,
  "LatestFinishedAt": null,
  "Team": "foundation",
}

###
POST {{$global.Host}}/v1/schedule/update/status HTTP/1.1
Content-Type: application/json

{
  "CronWorkflowId": "02003827-4086-4638-a562-e893351a3cfd",
  "ScheduleJobName": "dev-foundation-test-job-sj",
  "LatestStatus": "Succeeded",
  "LatestWorkflowName": null,
  "LatestWorkflowId": "6b2f4a8b-e34e-4067-b3f9-9245d672b18e",
  "LatestStartedAt": "2024/11/1 3:30:00",
  "LatestFinishedAt": "2024/11/1 3:31:10",
  "Team": "foundation",
}
###
POST {{$global.Host}}/v1/schedule/execute?namespace=foundation&scheduleJobName=dev-foundation-test-sj HTTP/1.1
accept: text/plain
Content-Type: application/x-www-form-urlencoded