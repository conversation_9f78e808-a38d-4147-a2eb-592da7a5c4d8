# 任務完成後的檢查清單

## 程式碼品質檢查

### 建置驗證
```bash
# 確保專案可以正常建置
dotnet build

# 檢查是否有建置警告
dotnet build --verbosity normal
```

### 測試執行
```bash
# 執行所有單元測試
dotnet test

# 確保測試覆蓋率符合標準
dotnet test --collect:"XPlat Code Coverage"
```

## 程式碼風格檢查

### EditorConfig 合規性
- 檢查縮排是否使用 4 個空格
- 確認檔案結尾有換行字元
- 驗證 using 指令放在命名空間外部

### 命名慣例檢查
- 介面名稱以 `I` 開頭
- 服務類別以 `Service` 結尾
- 列舉以 `Enum` 結尾
- 工具類別以 `Utility` 結尾

## 功能驗證

### API 測試
```bash
# 啟動 API 服務
dotnet run --project src/JKOPay.BatchSystem.Api

# 檢查健康檢查端點
curl http://localhost:8080/healthz
curl http://localhost:8080/healthz/liveness
curl http://localhost:8080/healthz/readiness
```

### 資料庫連線測試
```bash
# 確保資料庫遷移正常
dotnet ef database update --project src/JKOPay.BatchSystem.Api

# 檢查資料庫連線
# (透過健康檢查端點驗證)
```

## 文件更新

### API 文件
- 確保 Swagger 文件正確生成
- 檢查 XML 註解是否完整
- 驗證 API 版本設定

### 程式碼註解
- 重要方法需要有中文註解
- 複雜邏輯需要說明
- 公開 API 需要完整的 XML 文件註解

## 部署準備

### Docker 映像建置
```bash
# 測試 Docker 映像建置
docker build -f src/JKOPay.BatchSystem.Api/Dockerfile -t test-api .
docker build -f src/JKOPay.BatchSystem.Operator/Dockerfile -t test-operator .
docker build -f src/JKOPay.BatchSystem.Job/Dockerfile -t test-job .
```

### 環境設定檢查
- 確認各環境的 appsettings 檔案正確
- 檢查環境變數設定
- 驗證 Kubernetes 部署檔案

## Git 提交前檢查
```bash
# 檢查 Git 狀態
git status

# 確保沒有不必要的檔案被追蹤
git diff --cached

# 提交前最後一次建置測試
dotnet build && dotnet test
```