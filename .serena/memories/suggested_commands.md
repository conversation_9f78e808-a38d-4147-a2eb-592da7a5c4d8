# 建議的開發指令

## 基本建置指令

### 還原和建置
```bash
# 還原套件
dotnet restore

# 建置整個解決方案
dotnet build

# 建置特定專案
dotnet build src/JKOPay.BatchSystem.Api/JKOPay.BatchSystem.Api.csproj

# 清理建置輸出
dotnet clean
```

### 執行應用程式
```bash
# 執行 API 專案
dotnet run --project src/JKOPay.BatchSystem.Api

# 執行 Operator 專案
dotnet run --project src/JKOPay.BatchSystem.Operator

# 執行 Job 專案
dotnet run --project src/JKOPay.BatchSystem.Job
```

## 測試指令
```bash
# 執行所有測試
dotnet test

# 執行特定測試專案
dotnet test test/JKOPay.BatchSystem.Core.Test

# 執行測試並產生覆蓋率報告
dotnet test --collect:"XPlat Code Coverage"
```

## 資料庫管理
```bash
# 更新資料庫內容 (使用專案腳本)
./script/update_dbcontext.sh

# Entity Framework 遷移
dotnet ef migrations add <MigrationName> --project src/JKOPay.BatchSystem.Core
dotnet ef database update --project src/JKOPay.BatchSystem.Api

# 查看遷移狀態
dotnet ef migrations list --project src/JKOPay.BatchSystem.Core
```

## Docker 相關指令
```bash
# 建立 API Docker 映像
docker build -f src/JKOPay.BatchSystem.Api/Dockerfile -t jkopay.batchsystem.api .

# 建立 Operator Docker 映像
docker build -f src/JKOPay.BatchSystem.Operator/Dockerfile -t jkopay.batchsystem.operator .

# 建立 Job Docker 映像
docker build -f src/JKOPay.BatchSystem.Job/Dockerfile -t jkopay.batchsystem.job .

# 使用 Docker Compose 啟動相依服務
docker-compose up -d mysql

# 停止所有服務
docker-compose down
```

## 本地開發環境設定
```bash
# 設定環境變數
export ASPNETCORE_ENVIRONMENT=local
export DB="Server=127.0.0.1;Database=batchsystem;User=batchuser;Password=batchpw;"

# 啟動 MySQL 服務 (使用 Docker Compose)
docker-compose up -d mysql
```

## macOS 系統工具指令
```bash
# 檔案操作
ls -la                    # 列出檔案詳細資訊
find . -name "*.cs"       # 尋找 C# 檔案
grep -r "search" *.cs     # 在 C# 檔案中搜尋文字

# Git 操作
git status               # 查看狀態
git add .               # 加入所有變更
git commit -m "message" # 提交變更
git push origin master  # 推送到遠端

# 程序管理
ps aux | grep dotnet    # 查看 dotnet 程序
kill -9 <PID>          # 強制終止程序
```