# 技術堆疊

## 核心技術
- **.NET 9.0**: 主要開發框架，使用 C# 語言
- **ASP.NET Core**: Web API 框架
- **Entity Framework Core**: ORM 框架，搭配 MySQL 資料庫
- **Kubernetes**: 容器編排平台
- **Argo Workflows**: 工作流程引擎
- **Argo Events**: 事件驅動系統
- **Apache Kafka**: 訊息佇列系統

## 主要套件和函式庫

### 核心套件
- `JKOPay.Platform.*`: JKOPay 內部平台套件
- `Microsoft.EntityFrameworkCore`: 資料存取
- `Pomelo.EntityFrameworkCore.MySql`: MySQL 提供者
- `Confluent.Kafka`: Kafka 客戶端
- `KubernetesClient`: Kubernetes API 客戶端

### 可觀測性
- `OpenTelemetry.*`: 分散式追蹤和指標
- `Serilog.*`: 結構化日誌記錄
- `Prometheus`: 指標收集

### API 和文件
- `Swashbuckle.AspNetCore`: OpenAPI/Swagger 文件
- `Asp.Versioning.*`: API 版本控制
- `Newtonsoft.Json`: JSON 序列化

### 測試框架
- `xunit`: 單元測試框架
- `FluentAssertions`: 斷言函式庫
- `NSubstitute`: 模擬框架

## 建置系統
- 使用 .NET Solution 檔案 (`JKOPay.BatchSystem.sln`)
- 採用中央套件管理 (`Directory.Packages.props`)
- 支援多目標框架 (net8.0/net9.0)