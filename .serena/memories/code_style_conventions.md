# 程式碼風格和慣例

## EditorConfig 設定
專案使用 `.editorconfig` 檔案定義統一的程式碼風格：

### 基本設定
- **行尾字元**: Unix 風格 (LF)
- **檔案結尾**: 必須有換行字元
- **縮排**: 使用空格，4 個空格為一個縮排層級

### C# 程式碼風格
- **變數宣告**: 
  - 內建型別使用 `var` (`csharp_style_var_for_built_in_types = true`)
  - 型別明顯時使用 `var` (`csharp_style_var_when_type_is_apparent = true`)
  - 其他情況不使用 `var` (`csharp_style_var_elsewhere = false`)

- **存取修飾詞**: 總是明確指定 (`dotnet_style_require_accessibility_modifiers = always`)

- **表達式主體**: 
  - 屬性使用表達式主體 (`csharp_style_expression_bodied_properties = true`)
  - 方法在單行時使用表達式主體 (`csharp_style_expression_bodied_methods = when_on_single_line`)

- **Using 指令**: 放在命名空間外部 (`csharp_using_directive_placement = outside_namespace`)

## 命名慣例
- **介面**: 以 `I` 開頭 (如 `IJobService`, `IArgoWorkflowUtility`)
- **服務類別**: 以 `Service` 結尾 (如 `JobService`, `WorkflowService`)
- **工具類別**: 以 `Utility` 結尾 (如 `ArgoWorkflowUtility`, `KafkaUtility`)
- **列舉**: 以 `Enum` 結尾 (如 `JobStatusEnum`, `JobPriorityEnum`)

## 專案結構慣例
- **Models**: 資料模型和 DTO
- **Services**: 業務邏輯服務
- **Controllers**: API 控制器
- **Extensions**: 擴充方法
- **Utilities**: 工具類別