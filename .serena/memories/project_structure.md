# 專案結構概覽

## 根目錄結構
```
jkopay.batchsystem/
├── src/                          # 原始碼目錄
│   ├── JKOPay.BatchSystem.Api/   # Web API 專案
│   ├── JKOPay.BatchSystem.Core/  # 核心業務邏輯
│   ├── JKOPay.BatchSystem.Operator/ # Kubernetes Operator
│   ├── JKOPay.BatchSystem.Job/   # 批次作業執行器
│   └── JKOPay.BatchSystem.DemoJob/ # 示範作業
├── test/                         # 測試專案目錄
│   ├── JKOPay.BatchSystem.Core.Test/
│   └── JKOPay.BatchSystem.Operator.Test/
├── k8s/                          # Kubernetes 部署檔案
├── script/                       # 開發腳本
├── doc/                          # 文件目錄
├── http/                         # HTTP 測試檔案
└── uml/                          # UML 圖表
```

## 核心專案說明

### JKOPay.BatchSystem.Api
- **目的**: 提供 RESTful API 介面
- **主要功能**: 
  - 作業管理 API
  - 排程管理 API
  - 工作流程管理 API
  - 健康檢查端點
- **關鍵檔案**:
  - `Program.cs`: 應用程式進入點和服務設定
  - `Controllers/`: API 控制器
  - `Models/`: 請求/回應模型

### JKOPay.BatchSystem.Core
- **目的**: 共用的核心業務邏輯和模型
- **主要功能**:
  - 資料模型定義
  - 業務邏輯服務
  - Argo Workflows 整合
  - Kafka 訊息處理
- **關鍵目錄**:
  - `Models/`: 資料模型和 DTO
  - `Services/`: 業務邏輯服務
  - `Utilities/`: 工具類別
  - `Extensions/`: 擴充方法

### JKOPay.BatchSystem.Operator
- **目的**: Kubernetes 控制器，監控工作流程狀態
- **主要功能**:
  - 監控 Argo Workflows 狀態變化
  - 更新作業執行狀態
  - 處理工作流程生命週期事件

### JKOPay.BatchSystem.Job
- **目的**: 實際執行批次作業的容器
- **主要功能**:
  - 執行具體的批次作業邏輯
  - 與外部系統整合
  - 作業結果回報

## 設定檔案

### 解決方案層級
- `JKOPay.BatchSystem.sln`: Visual Studio 解決方案檔案
- `Directory.Packages.props`: 中央套件版本管理
- `.editorconfig`: 程式碼風格設定
- `docker-compose.yml`: 本地開發環境設定

### 部署相關
- `.gitlab-ci.yml`: GitLab CI/CD 流水線設定
- `k8s/`: Kubernetes 部署檔案
- `Dockerfile`: 各專案的容器化設定