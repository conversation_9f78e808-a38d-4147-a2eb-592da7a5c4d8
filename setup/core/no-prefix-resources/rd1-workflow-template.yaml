apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: batchsystem-main-cron-workflow-template
  namespace: rd1
  labels:
    batchsystem.owner: batchsystem
    environment: dev
spec:
  serviceAccountName: batchsystem-sa
  entrypoint: main
  arguments:
    parameters:
      - name: jobName
        value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
      - name: templateName
        value: default
      - name: jobData
        value: default
      - name: priority
        value: default
      - name: team
        value: "{{workflow.namespace}}"
      - name: environment
        value: "{{workflow.labels.environment}}"
      - name: apiUrl
        value: "{{workflow.labels.environment}}-foundation-jkopay-batchsystem-api-svc.svc.cluster.local:9090"
  templates:
    - name: main
      metrics:
        prometheus:
          - name: wf_execute_result_counter
            help: "Count of execution by result status"
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
              - key: result
                value: "{{status}}"
              - key: crateTimestamp
                value: "{{workflow.creationTimestamp}}"
              - key: finishTimestamp
                value: "{{workflow.status.finishedAt}}"
            counter:
              value: "1"
          - name: wf_execution_time
            help: "Time of execution by result status"
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
            gauge:
              realtime: true
              value: "{{workflow.duration}}"
      dag:
        tasks:
          - name: create-job-record
            template: create-job-record
            arguments:
              parameters:
                - name: jobName
                  value: "{{workflow.parameters.jobName}}"
                - name: status
                  value: "WorkflowStarted"
                - name: priority
                  value: "{{workflow.parameters.priority}}"
                - name: templateName
                  value: "{{workflow.parameters.templateName}}"
                - name: team
                  value: "{{workflow.parameters.team}}"
          - name: started-status
            dependencies: [create-job-record] # 使用 dependencies 屬性聲明依賴關係
            template: update-status
            arguments:
              parameters:
                - name: jobId
                  value: "{{tasks.create-job-record.outputs.parameters.jobId}}"
                - name: status
                  value: "WorkflowStarted"
                - name: priority
                  value: "{{workflow.parameters.priority}}"
          - name: team-job
            dependencies: [started-status] # 使用 dependencies 属性声明依赖关系
            templateRef:
              name: "{{workflow.parameters.templateName}}"
              template: "main"
            arguments:
              parameters: # 将 jobData 参数传递给调用的 WorkflowTemplate
                - name: jobData
                  value: "{{workflow.parameters.jobData}}"
          - name: complete-workflow
            dependencies: [team-job] # 确保 complete-workflow 在 team-job 成功后执行
            template: update-status
            arguments:
              parameters:
                - name: jobId
                  value: "{{tasks.create-job-record.outputs.parameters.jobId}}"
                - name: status
                  value: "WorkflowComplete"
                - name: priority
                  value: "{{workflow.parameters.priority}}"
    - name: create-job-record
      inputs:
        parameters:
          - name: jobName
          - name: templateName
          - name: status
          - name: priority
          - name: team
      container:
        image: curlimages/curl:7.85.0
        command: [sh, -c]
        args:
          - |
            response=$(curl --silent --request POST --url http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule \
            --header 'Accept: */*' \
            --header 'Content-Type: application/json' \
            --header 'User-Agent: httpyac' \
            --data '{
              "jobName": "{{inputs.parameters.jobName}}",
              "workflowId": "{{workflow.uid}}",
              "workflowName": "{{workflow.name}}",
              "workflowTemplateName": "{{inputs.parameters.templateName}}",
              "status": "{{inputs.parameters.status}}",
              "priority": "{{inputs.parameters.priority}}",
              "team": "{{inputs.parameters.team}}"
            }')
            echo "Full Response: $response"
            jobId=$(echo $response | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')
            if [ -z "$jobId" ]; then
              echo "Error: Job ID not found in the response. Response: $response" >&2
              exit 1
            fi
            echo "Extracted jobId: $jobId"
            echo $jobId > /tmp/jobId
      outputs:
        parameters:
          - name: jobId
            valueFrom:
              path: /tmp/jobId
    - name: update-status
      inputs:
        parameters:
          - name: jobId
          - name: status
          - name: priority
      http:
        url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
        method: POST
        timeoutSeconds: 10
        successCondition: 'response.body contains "0001"'
        headers:
          - name: Content-Type
            value: application/json
        body: |
          {
            "jobId": "{{inputs.parameters.jobId}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }
