apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: batchsystem-main-cron-workflow-template
  namespace: foundation
  labels:
    batchsystem.owner: batchsystem
    environment: dev
spec:
  serviceAccountName: foundation-batchsystem-sa
  entrypoint: main
  arguments:
    parameters:
      - name: jobName
        value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
      - name: templateName
        value: default
      - name: jobData
        value: default
      - name: priority
        value: schedule
      - name: team
        value: "{{workflow.namespace}}"
      - name: environment
        value: "ENVIRONMENT_PLACEHOLDER"
      - name: apiUrl
        value: "{{workflow.labels.environment}}-foundation-jkopay-batchsystem-api-svc.svc.cluster.local:9090"
  templates:
    - name: main
      metrics:
        prometheus:
          - name: wf_execute_result_counter
            help: "Count of execution by result status"
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
              - key: result
                value: "{{status}}"
              - key: crateTimestamp
                value: "{{workflow.creationTimestamp}}"
              - key: finishTimestamp
                value: "{{workflow.status.finishedAt}}"
            counter:
              value: "1"
          - name: wf_execution_time
            help: "Time of execution by result status"
            labels:
              - key: team
                value: "{{workflow.namespace}}"
              - key: workflowName
                value: "{{workflow.parameters.templateName}}"
            gauge:
              realtime: true
              value: "{{workflow.duration}}"
      dag:
        tasks:
          - name: create-job-record
            template: create-job-record
            arguments:
              parameters:
                - name: jobName
                  value: "{{workflow.parameters.jobName}}"
                - name: status
                  value: "WorkflowStarted"
                - name: priority
                  value: "{{workflow.parameters.priority}}"
                - name: templateName
                  value: "{{workflow.parameters.templateName}}"
                - name: team
                  value: "{{workflow.parameters.team}}"
          - name: started-status
            dependencies: [create-job-record]
            template: update-status
            arguments:
              parameters:
                - name: jobId
                  value: "{{tasks.create-job-record.outputs.parameters.jobId}}"
                - name: status
                  value: "WorkflowStarted"
                - name: priority
                  value: "{{workflow.parameters.priority}}"
          - name: team-job
            dependencies: [started-status]
            templateRef:
              name: "{{workflow.parameters.templateName}}"
              template: "main"
            arguments:
              parameters:
                - name: jobData
                  value: "{{workflow.parameters.jobData}}"
          - name: complete-workflow
            dependencies: [team-job]
            template: update-status
            arguments:
              parameters:
                - name: jobId
                  value: "{{tasks.create-job-record.outputs.parameters.jobId}}"
                - name: status
                  value: "WorkflowComplete"
                - name: priority
                  value: "{{workflow.parameters.priority}}"
    - name: create-job-record
      inputs:
        parameters:
          - name: jobName
          - name: templateName
          - name: status
          - name: priority
          - name: team
      container:
        image: curlimages/curl:7.85.0
        command: [sh, -c]
        args:
          - |
            echo "API URL: {{workflow.parameters.apiUrl}}"

            # 處理 jobName，移除環境前綴 (dev-, sit-, uat-, prod-)
            original_job_name="{{inputs.parameters.jobName}}"
            processed_job_name="$original_job_name"

            # 檢查並移除環境前綴
            case "$original_job_name" in
              dev-*)
                processed_job_name="${original_job_name#dev-}"
                echo "Removed 'dev-' prefix from jobName: $original_job_name -> $processed_job_name"
                ;;
              sit-*)
                processed_job_name="${original_job_name#sit-}"
                echo "Removed 'sit-' prefix from jobName: $original_job_name -> $processed_job_name"
                ;;
              uat-*)
                processed_job_name="${original_job_name#uat-}"
                echo "Removed 'uat-' prefix from jobName: $original_job_name -> $processed_job_name"
                ;;
              prod-*)
                processed_job_name="${original_job_name#prod-}"
                echo "Removed 'prod-' prefix from jobName: $original_job_name -> $processed_job_name"
                ;;
              *)
                echo "No environment prefix found in jobName: $original_job_name"
                ;;
            esac

            response=$(curl --silent --request POST --url http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule \
            --header 'Accept: */*' \
            --header 'Content-Type: application/json' \
            --header 'User-Agent: httpyac' \
            --data '{
              "jobName": "'$processed_job_name'",
              "workflowId": "{{workflow.uid}}",
              "workflowName": "{{workflow.name}}",
              "workflowTemplateName": "{{inputs.parameters.templateName}}",
              "status": "{{inputs.parameters.status}}",
              "priority": "{{inputs.parameters.priority}}",
              "team": "{{inputs.parameters.team}}"
            }')
            echo "Full Response: $response"
            jobId=$(echo $response | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')
            if [ -z "$jobId" ]; then
              echo "Error: Job ID not found in the response. Response: $response" >&2
              exit 1
            fi
            echo "Extracted jobId: $jobId"
            echo $jobId > /tmp/jobId
      outputs:
        parameters:
          - name: jobId
            valueFrom:
              path: /tmp/jobId

    - name: update-status
      inputs:
        parameters:
          - name: jobId
          - name: status
          - name: priority
      script:
        image: curlimages/curl:7.85.0
        command: [sh]
        source: |
          echo "Updating job status via API URL: {{workflow.parameters.apiUrl}}"
          echo "Job ID: {{inputs.parameters.jobId}}, Status: {{inputs.parameters.status}}"

          response=$(curl --silent --request POST --url http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status \
          --header 'Content-Type: application/json' \
          --data '{
            "jobId": "{{inputs.parameters.jobId}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }')

          echo "Response: $response"

          if echo "$response" | grep -q "0001"; then
            echo "Status update successful"
            exit 0
          else
            echo "Status update failed"
            exit 1
          fi
