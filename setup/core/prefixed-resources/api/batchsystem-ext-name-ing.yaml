apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: foundation-batchsystem-ing
  namespace: foundation
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - "*.foundation.dev"
      secretName: foundation-dev-tls
  rules:
    - host: batchsystem.foundation.dev
      http:
        paths:
          - pathType: ImplementationSpecific
            path: /
            backend:
              service:
                name: foundation-jkopay-batchsystem-api-svc
                port:
                  number: 9090
