apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namePrefix: dev-
resources:
  - ../../base
  - ../../../jobs
  - ../../../utils/overlays/dev
  - ./batchsystem-ext-name-ing.yaml
configurations:
  - kustomconfig.yaml
patches:
  - path: ./jobs/rd1-job-wt-patch.yaml
  - path: ./jobs/rd1-test-job-sj-patch.yaml
  - path: ./jobs/foundation-job-wt-patch.yaml
  - path: ./jobs/foundation-test-job-sj-patch.yaml
  - path: ./jobs/foundation-scrape-schedule-job-status-job-wt-patch.yaml
  - path: ./jobs/foundation-scrape-schedulejob-status-job-sj-patch.yaml
  - path: ./vault-auth-patch.yaml
  - path: ./vault-secret-patch.yaml
  - path: batchsystem-api-deployment-patch.yaml
    target:
      kind: Deployment
      name: foundation-jkopay-batchsystem-api
      namespace: foundation
  - target:
      kind: EventSource
      name: kafka-eventsource
    patch: |
      - op: replace
        path: /spec/kafka/priority/sasl/passwordSecret/name
        value: kafka-user
  - target:
      kind: EventSource
      name: kafka-eventsource
    patch: |
      - op: replace
        path: /spec/kafka/priority/sasl/userSecret/name
        value: kafka-user
  - target:
      kind: Sensor
      name: kafka-sensor
    patch: |
      - op: replace
        path: /spec/dependencies/0/eventSourceName
        value: dev-kafka-eventsource
images:
  - name: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.api
    newTag: 0.0.0.160
  - name: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.job
    newTag: 0.0.0.57
  - name: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.operator
    newTag: 0.0.0.81
