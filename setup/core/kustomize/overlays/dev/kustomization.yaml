apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ./no-prefix
  - ./prefixed
configurations:
  - kustomconfig.yaml

# labels:
# 為所有資源添加 environment 標籤 - 會導致 Deployment selector 不可變更錯誤
# - pairs:
#     environment: dev

patches:
  # 環境特定的 patches
  - path: ./patches/vault-auth-patch.yaml
  - path: ./patches/vault-secret-patch.yaml
  - path: ./patches/batchsystem-api-deployment-patch.yaml
  - path: ./patches/jobs/foundation-job-wt-patch.yaml
  - path: ./patches/jobs/foundation-test-job-sj-patch.yaml
  - path: ./patches/jobs/foundation-scrape-schedule-job-status-job-wt-patch.yaml
  - path: ./patches/jobs/foundation-scrape-schedulejob-status-job-sj-patch.yaml
  - path: ./patches/jobs/rd1-job-wt-patch.yaml
  - path: ./patches/jobs/rd1-test-job-sj-patch.yaml
  # Kafka 相關的 inline patches
  - target:
      kind: EventSource
      name: dev-kafka-eventsource
    patch: |
      - op: replace
        path: /spec/kafka/priority/sasl/passwordSecret/name
        value: kafka-user
  - target:
      kind: EventSource
      name: dev-kafka-eventsource
    patch: |
      - op: replace
        path: /spec/kafka/priority/sasl/userSecret/name
        value: kafka-user
  - target:
      kind: Sensor
      name: dev-kafka-sensor
    patch: |
      - op: replace
        path: /spec/dependencies/0/eventSourceName
        value: dev-kafka-eventsource

images:
  - name: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.api
    newTag: 0.0.0.160
  - name: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.job
    newTag: 0.0.0.57
  - name: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.operator
    newTag: 0.0.0.81
