apiVersion: apps/v1
kind: Deployment
metadata:
  name: foundation-jkopay-batchsystem-api
  namespace: foundation # 可以根据实际情况修改命名空间
  labels:
    environment: dev
    app: foundation-jkopay-batchsystem-api
spec:
  replicas: 1 # 您可以根据需要调整副本数量
  selector:
    matchLabels:
      app: foundation-jkopay-batchsystem-api
  template:
    metadata:
      labels:
        app: foundation-jkopay-batchsystem-api
      annotations:
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/role: "foundation"
        vault.hashicorp.com/agent-inject-secret-env-config: "secrets/data/sit/foundation/app/jkopay-batchsystem-api"
        vault.hashicorp.com/agent-inject-template-env-config: |
          {{- with secret "secrets/data/sit/foundation/app/jkopay-batchsystem-api" -}}
          #!/bin/sh
          {{- range $k, $v := .Data.data }}
          export {{ $k }}='{{ $v }}'
          {{- end }}
          exec "$@"
          {{- end }}
        vault.hashicorp.com/agent-inject-command-env-config: "cat /vault/secrets/env-config"
    spec:
      serviceAccountName: foundation-batchsystem-sa
      containers:
        - name: jkopay-batchsystem-api
          image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.api:0.0.0.140
          command: [
              "/bin/sh",
              "-c",
              '
              if [ -f "/vault/secrets/env-config" ]; then
              source "/vault/secrets/env-config";
              fi;
              env;
              dotnet JKOPay.BatchSystem.Api.dll {{workflow.uid}} {{workflow.name}} {{workflow.namespace}} {{inputs.parameters.jobData}}',
            ]
          ports:
            - containerPort: 9090
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          env:
            - name: ASPNETCORE_ENVIRONMENT
              value: "development"
            - name: TEST
              value: "test"
          readinessProbe:
            httpGet:
              path: /healthz/liveness # 根据您的API健康检查路径
              port: 9090
            initialDelaySeconds: 15
            periodSeconds: 5
            timeoutSeconds: 3
            successThreshold: 3
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /healthz/liveness # 根据您的API健康检查路径
              port: 9090
            initialDelaySeconds: 50
            periodSeconds: 10
