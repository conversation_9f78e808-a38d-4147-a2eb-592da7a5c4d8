apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  name: rd1-test-job-sj
  namespace: rd1
  labels:
    enviorment: dev
spec:
  schedule: "*/5 */5 * * *"
  workflowSpec:
    metrics:
      prometheus:
        - name: cronwf_execute_result_counter
          help: "Count of execution by result status"
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
            - key: workflowId
              value: "{{workflow.uid}}"
            - key: scheduledTime
              value: "{{workflow.scheduledTime}}"
          counter:
            value: "1"
        - name: cronwf_execute_duration
          help: "Duration of cron workflow execution"
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
          gauge:
            realtime: false
            value: "{{workflow.duration}}"
    workflowTemplateRef:
      name: dev-rd1-batchsystem-main-cron-workflow-template
    arguments:
      parameters:
        - name: jobName
          value: rd1-test-job-sj
        - name: templateName
          value: dev-rd1-job-wt
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInNsYWNram9iIiB9
        - name: priority
          value: schedule
        - name: team
          value: rd1
