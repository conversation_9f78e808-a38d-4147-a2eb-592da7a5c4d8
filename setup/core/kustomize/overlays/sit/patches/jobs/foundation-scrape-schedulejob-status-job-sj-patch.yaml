apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  # 可以自行決定命名 但請符合格式規範 => {team}-{your-job-name}-sj
  name: foundation-scrape-schedule-job-status-job-sj
  # 請使用團隊名稱
  labels:
    app: foundation-scrape-schedule-job-status-job-sj
    developer: foundation
    service: schedule-job
    environment: sit
  namespace: foundation
  annotations:
    workflows.argoproj.io/description: |
      Schedule job to periodically call scrape-schedule-job-status-job-wt
    workflows.argoproj.io/version: ">= 3.2.0"
spec:
  # 每五分鐘執行一次
  schedule: "*/5 * * * *"
  workflowSpec:
    metadata:
      labels:
        environment: sit
    metrics:
      prometheus:
        - name: cronwf_execute_result_counter
          help: Count of execution by result status
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
            - key: workflowId
              value: "{{workflow.uid}}"
            - key: scheduledTime
              value: "{{workflow.scheduledTime}}"
          counter:
            value: "1"
        - name: cronwf_execute_duration
          help: Duration of cron workflow execution
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
          gauge:
            realtime: false
            value: "{{workflow.duration}}"
    workflowTemplateRef:
      name: batchsystem-main-cron-workflow-template
    arguments:
      parameters:
        # 使用變數動態組合模板名稱，包含環境前綴
        - name: templateName
          value: sit-foundation-scrape-schedule-job-status-job-wt
        # JOB 所需要的參數 請使用 base64 字串
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInNjcmFwZS1zY2hlZHVsZS1qb2Itc3RhdHVzLWpvYiIgfQ==