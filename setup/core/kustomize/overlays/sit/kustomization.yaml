apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namePrefix: sit-
resources:
  - ../../base
  - ../../template/overlays/local
  - ../../job
  # - ./batchsystem-ext-name-ing-patch.yaml
  - ./batchsystem-secret.yaml
patches:
  - path: ./deployment-api-patch.yaml
  - path: ./deployment-opa-patch.yaml
  - path: ./foundation-general-schedule-job-patch.yaml
  - path: ./foundation-update-job-status-template-patch.yaml
  - target:
      kind: EventSource
      name: kafka-eventsource
    patch: |
      - op: replace
        path: /spec/kafka/priority/sasl/passwordSecret/name
        value: sit-kafka-user
  - target:
      kind: EventSource
      name: kafka-eventsource
    patch: |
      - op: replace
        path: /spec/kafka/priority/sasl/userSecret/name
        value: sit-kafka-user
  - target:
      kind: Sensor
      name: kafka-sensor
    patch: |
      - op: replace
        path: /spec/dependencies/0/eventSourceName
        value: sit-kafka-eventsource
