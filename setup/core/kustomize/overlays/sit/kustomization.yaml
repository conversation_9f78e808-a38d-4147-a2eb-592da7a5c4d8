apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ./no-prefix
  - ./prefixed

configurations:
  - kustomconfig.yaml

configMapGenerator:
  - name: env-vars
    options:
      disableNameSuffixHash: true
    literals:
      - environment=sit
      - apiUrl=sit-foundation-jkopay-batchsystem-api-svc.foundation:9090

# labels:
# 為所有資源添加 environment 標籤 - 會導致 Deployment selector 不可變更錯誤
# - pairs:
#     environment: sit

patches:
  # 環境特定的 patches
  - path: ./patches/vault-auth-patch.yaml
  - path: ./patches/vault-secret-patch.yaml
  - path: ./patches/batchsystem-api-deployment-patch.yaml
  - path: ./patches/jobs/foundation-job-wt-patch.yaml
  - path: ./patches/jobs/foundation-test-job-sj-patch.yaml
  - path: ./patches/jobs/foundation-scrape-schedule-job-status-job-wt-patch.yaml
  - path: ./patches/jobs/foundation-scrape-schedulejob-status-job-sj-patch.yaml
  - path: ./patches/jobs/rd1-job-wt-patch.yaml
  - path: ./patches/jobs/rd1-test-job-sj-patch.yaml

  # Kafka 相關的 inline patches
  - target:
      kind: EventSource
      name: sit-kafka-eventsource
    patch: |
      - op: replace
        path: /spec/kafka/priority/sasl/passwordSecret/name
        value: kafka-user
  - target:
      kind: EventSource
      name: sit-kafka-eventsource
    patch: |
      - op: replace
        path: /spec/kafka/priority/sasl/userSecret/name
        value: kafka-user
  - target:
      kind: Sensor
      name: sit-kafka-sensor
    patch: |
      - op: replace
        path: /spec/dependencies/0/eventSourceName
        value: sit-kafka-eventsource

replacements:
  # 替換 WorkflowTemplate 中的環境值
  - source:
      kind: ConfigMap
      name: env-vars
      fieldPath: data.environment
    targets:
      - select:
          kind: WorkflowTemplate
          name: batchsystem-main-cron-workflow-template
          namespace: foundation
        fieldPaths:
          - spec.arguments.parameters.[name=environment].value
      - select:
          kind: WorkflowTemplate
          name: batchsystem-main-cron-workflow-template
          namespace: rd1
        fieldPaths:
          - spec.arguments.parameters.[name=environment].value
  # 替換 CronWorkflow 中的環境值
  - source:
      kind: ConfigMap
      name: env-vars
      fieldPath: data.environment
    targets:
      - select:
          kind: CronWorkflow
          name: sit-rd1-test-job-sj
        fieldPaths:
          - spec.workflowSpec.arguments.parameters.[name=environment].value
  # 替換 WorkflowTemplate 中的 API URL
  - source:
      kind: ConfigMap
      name: env-vars
      fieldPath: data.apiUrl
    targets:
      - select:
          kind: WorkflowTemplate
          name: batchsystem-main-cron-workflow-template
          namespace: foundation
        fieldPaths:
          - spec.arguments.parameters.[name=apiUrl].value
      - select:
          kind: WorkflowTemplate
          name: batchsystem-main-cron-workflow-template
          namespace: rd1
        fieldPaths:
          - spec.arguments.parameters.[name=apiUrl].value
      - select:
          kind: WorkflowTemplate
          name: sit-foundation-batchsystem-workflow-template
          namespace: foundation
        fieldPaths:
          - spec.arguments.parameters.[name=apiUrl].value
      - select:
          kind: WorkflowTemplate
          name: sit-rd1-batchsystem-workflow-template
          namespace: rd1
        fieldPaths:
          - spec.arguments.parameters.[name=apiUrl].value

images:
  - name: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.api
    newTag: 0.0.0.160
  - name: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.job
    newTag: 0.0.0.57
  - name: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.operator
    newTag: 0.0.0.81