apiVersion: apps/v1
kind: Deployment
metadata:
  name: jkopay-batchsystem-api
  namespace: foundation # 可以根据实际情况修改命名空间
spec:
  replicas: 1 # 您可以根据需要调整副本数量
  selector:
    matchLabels:
      app: jkopay-batchsystem-api
  template:
    metadata:
      labels:
        app: jkopay-batchsystem-api
    spec:
      containers:
        - name: jkopay-batchsystem-api
          image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.api:0.0.0.77
          ports:
            - containerPort: 9090
          env:
            - name: ASPNETCORE_ENVIRONMENT
              value: "sit"
            - name: KAFKA_USERNAME
              valueFrom:
                secretKeyRef:
                  name: sit-foundation-kafka-user
                  key: user
            - name: KAFKA_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: sit-foundation-kafka-user
                  key: password
            #
            # - name: ConnectionStrings__MySql
            #   valueFrom:
            #     secretKeyRef:
            #       name: sit-foundation-batchsystem-mysql
            #       key: key
          readinessProbe:
            httpGet:
              path: /healthz # 根据您的API健康检查路径
              port: 9090
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /healthz # 根据您的API健康检查路径
              port: 9090
            initialDelaySeconds: 15
            periodSeconds: 20
