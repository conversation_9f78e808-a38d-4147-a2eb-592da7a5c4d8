apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: foundation-scrape-schedule-job-status-job-wt
  namespace: foundation
spec:
  serviceAccountName: foundation-sa
  templates:
    - name: main
      steps:
        - - name: call-api
            template: http-request
            arguments:
              parameters: [{name: url, value: "http://dev-foundation-jkopay-batchsystem-api-svc.foundation:9090/api/v1/meter/scheduleJobList"}]

    - name: http-request
      inputs:
        parameters:
          - name: url
      http:
        url: "{{inputs.parameters.url}}"
        method: "GET"
        successCondition: "response.statusCode == 200"
        timeout: "60s"
      outputs:
        parameters:
          - name: response-body
            valueFrom:
              jsonPath: "{$.result}"
