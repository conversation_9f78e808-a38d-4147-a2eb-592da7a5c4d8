apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: rd1-job-wt
  namespace: rd1
spec:
  serviceAccountName: rd1-batchsystem-sa
  arguments:
    parameters:
      - name: jobData
        value: default
  templates:
    - name: main
      metadata:
        annotations:
          vault.hashicorp.com/agent-inject: "true"
          vault.hashicorp.com/role: "foundation"
          vault.hashicorp.com/agent-inject-secret-env-config: "secrets/data/sit/foundation/app/jkopay-batchsystem-api"
          vault.hashicorp.com/agent-inject-template-env-config: |
            {{- with secret "secrets/data/sit/foundation/app/jkopay-batchsystem-api" -}}
            #!/bin/sh
            {{- range $k, $v := .Data.data }}
            export {{ $k }}='{{ $v }}'
            {{- end }}
            exec "$@"
            {{- end }}
          vault.hashicorp.com/agent-inject-command-env-config: "cat /vault/secrets/env-config"
      inputs:
        parameters:
          - name: jobData
      container:
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.job:0.0.0.52
        command: ["source /vault/secrets/env-config"]
        args:
          [
            "{{workflow.uid}}",
            "{{workflow.name}}",
            "{{workflow.namespace}}",
            "{{inputs.parameters.jobData}}",
          ]
        env:
          - name: NETCORE_ENVIRONMENT
            value: development
          - name: IS_BASE64
            value: "true"
