apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  # 可以自行決定命名 但請符合格式規範 => {team}-{your-job-name}-sj
  name: foundation-test-job-sj
  # 請使用團隊名稱
  namespace: foundation
  annotations:
    workflows.argoproj.io/description: |
      Shared CronWorkflow Process
    workflows.argoproj.io/version: ">= 3.2.0"
spec:
  schedule: "*/2 */5 * *"
  workflowSpec:
    metrics:
      prometheus:
        - name: cronworkflow_result_counter
          help: "Count of execution by result status"
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{workflow.status}}"
          counter:
            value: "1"
        - name: wf_exec_duration_gauge
          help: "Duration gauge by workflow name and status"
          labels:
            - key: name
              value: "{{workflow.labels.workflows.argoproj.io/cron-workflow}}"
            - key: status
              value: "{{status}}"
          gauge:
            value: "{{workflow.duration}}"
            realTime: true
    workflowTemplateRef:
      # 請加上環境前綴
      name: dev-foundation-batchsystem-main-cron-workflow-template
    arguments:
      parameters:
        # 使請用和 metadata.name 一致
        - name: jobName
          value: foundation-test-job-sj
        # 要使用的模板名稱, 請加上環境前綴
        - name: templateName
          value: dev-foundation-job-wt
        # JOB 所需要的參數 請使用 base64 字串
        - name: jobData
          value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInRlc3Rqb2IiIH0=
        - name: priority
          value: schedule
        # 請和上方的 metadata.namespace 一致
        - name: team
          value: rd1
