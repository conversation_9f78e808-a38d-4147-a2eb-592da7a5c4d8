﻿using System.Diagnostics;
using System.Net.Security;
using JKOPay.BatchSystem.Operator.Extensions;
using JKOPay.BatchSystem.Operator.Models;
using JKOPay.Platform.OpenTelemetry;
using JKOPay.Platform.HttpClientExtension;
using k8s;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OpenTelemetry.Exporter;
using Serilog;
using Serilog.Exceptions;
using Serilog.HttpClient.Extensions;
using Serilog.Sinks.OpenTelemetry;

namespace JKOPay.BatchSystem.Operator
{
    internal class Program
    {

        [Obsolete("Obsolete")]
        static async Task Main(string[] _)
        {
            var env = Environment.GetEnvironmentVariable("NETCORE_ENVIRONMENT");
            var team = Environment.GetEnvironmentVariable("NAMESPACE");
            var host = Host.CreateDefaultBuilder()
                .ConfigureHostConfiguration(config =>
                {
                    config.Sources.Clear();
                    config.AddEnvironmentVariables();
                    config.AddYamlFile("appsettings.yaml", false, false);
                    config.AddYamlFile($"appsettings.{env}.yaml", false, false);
                })
                .UseSerilog((ctx, _, lc) =>
                {
                    lc.ReadFrom.Configuration(ctx.Configuration)
                        .Enrich.FromLogContext()
                        .AddJsonDestructuringPolicies()
                        .Enrich.WithExceptionDetails();
                    if (env == "local")
                    {
                        lc.WriteTo.OpenTelemetry(options =>
                        {
                            options.Endpoint = "http://**********:30317";
                            options.Protocol = OtlpProtocol.Grpc;
                            options.ResourceAttributes = new Dictionary<string, object>
                            {
                                ["service.name"] = "BatchSystem.Operator.Serilog",
                                ["service.namespace"] = "JKOPay",
                                ["service.instance.id"] = "BatchSystem.Operator.Serilog-1.0.0",
                                ["service.version"] = "1.0.0",
                                ["service.environment"] = "local",
                                ["index"] = 10,
                                ["flag"] = true,
                                ["value"] = 3.14,
                                ["log.type"] = "serilog",
                            };

                            options.IncludedData =
                                IncludedData.SpanIdField
                                | IncludedData.TraceIdField
                                | IncludedData.TemplateBody
                                | IncludedData.MessageTemplateTextAttribute
                                | IncludedData.MessageTemplateRenderingsAttribute
                                | IncludedData.SourceContextAttribute
                                | IncludedData.MessageTemplateMD5HashAttribute;

                            options.BatchingOptions.BatchSizeLimit = 10;
                            options.BatchingOptions.BufferingTimeLimit = TimeSpan.FromSeconds(1);
                            options.BatchingOptions.QueueLimit = 5;
                        });
                    }
                })
                .ConfigureServices((contextBuilder, services) =>
                {
                    services.AddHttpClient("general", client =>
                    {
                        var activity = Activity.Current;
                        client.DefaultRequestHeaders.Add("X-Correlation-Id", Guid.NewGuid().ToString());
                        client.DefaultRequestHeaders.Add("X-Parent-Id", activity?.SpanId.ToString());
                    }).ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
                    {
                        ServerCertificateCustomValidationCallback =
                            HttpClientHandler.DangerousAcceptAnyServerCertificateValidator,
                    });

                    services.RegisterHttpClientsWithConditionalLogging("general",
                        configureOptions: options =>
                        {
                            // 一般日誌模式設為總是記錄（用於記錄基本請求資訊）
                            options.GeneralExtendedLogMode =
                                Platform.HttpClientExtension.Models.ExtendedLogMode.Always;

                            // 請求相關的日誌設定
                            options.RequestHeaderExtendedLogMode =
                                Platform.HttpClientExtension.Models.ExtendedLogMode.OnError;
                            options.RequestBodyExtendedLogMode = Platform.HttpClientExtension.Models
                                .ExtendedLogMode.ConditionalLogging;

                            // 請求和回應的 body 以及回應標頭都使用相同的條件式日誌記錄邏輯
                            var requestResponseLoggingCondition =
                                new JKOPay.Platform.HttpClientExtension.Models.ConditionalLoggingOptions
                                {
                                    CustomCondition = (request, response, exception) =>
                                    {
                                        // 如果有異常，總是記錄
                                        if (exception != null) return true;
                                        if (response == null) return true;

                                        // 當狀態碼不為 200 時
                                        return response.StatusCode != System.Net.HttpStatusCode.OK;
                                    }
                                };

                            // 請求 body 使用條件式日誌記錄
                            options.RequestBodyConditionalOptions = requestResponseLoggingCondition;

                            // 回應標頭使用條件式日誌記錄
                            options.ResponseHeaderExtendedLogMode = Platform.HttpClientExtension.Models
                                .ExtendedLogMode.OnError;
                            options.ResponseHeaderConditionalOptions = requestResponseLoggingCondition;

                            // 回應 body 使用相同的條件式日誌記錄
                            options.ResponseBodyExtendedLogMode = Platform.HttpClientExtension.Models
                                .ExtendedLogMode.ConditionalLogging;
                            options.ResponseBodyConditionalOptions = requestResponseLoggingCondition;
                        },
                        configureClient: httpClient =>
                        {
                            var activity = Activity.Current;
                            httpClient.DefaultRequestHeaders.Add("X-Correlation-Id", Guid.NewGuid().ToString());
                            httpClient.DefaultRequestHeaders.Add("X-Parent-Id", activity?.SpanId.ToString());
                            httpClient.Timeout = TimeSpan.FromSeconds(30);
                        },
                        configurePrimaryHandler: () => new HttpClientHandler
                        {
                            // 自訂 SSL 憑證驗證邏輯
                            ServerCertificateCustomValidationCallback = (sender, certificate, chain, sslPolicyErrors) => env == "local"
                        },
                        forwardDefaultHeaders: false);

                    services.AddOption<BatchSystemApiOption>("BatchSystemAPI");
                    services.AddKeyedSingleton("Namespace", team ?? "default");
                    services.AddSingleton<IKubernetesClientFactory>(_ =>
                    {
                        var config = env == "local"
                            ? KubernetesClientConfiguration.BuildConfigFromConfigFile()
                            : KubernetesClientConfiguration.BuildDefaultConfig();
                        return new KubernetesClientFactory(env!, config);
                    });
                    services.AddSingleton<IKubernetes>(sp =>
                    {
                        var logger = sp.GetRequiredService<ILogger>();
                        var factory = sp.GetRequiredService<IKubernetesClientFactory>();

                        logger.Information("In create kube config process. Env: {env}", env);
                        return factory.CreateClient();
                    });

                    var otlpHost = contextBuilder.Configuration["OpenTelemetry:Exporter:Otlp:Host"];
                    var port = contextBuilder.Configuration["OpenTelemetry:Exporter:Otlp:Port"];
                    services.AddOpentelemetry(serviceName: "JKOPay.BatchSystem.Operator",
                        endPoint: $"{otlpHost}:{port}",
                        protocol: OtlpExportProtocol.Grpc,
                        configTracing: _ => { },
                        configMetrics: _ => { },
                        meterNames: ["BatchSystem.Operator"]
                    );

                    services.AddCustomMetrics<CustomMetricsProvider>();
                    services.AddHostedService<ScheduleJobOperator>();
                    services.AddHostedService<WorkflowOperator>();
                    services.AddHostedService<HttpListenerService>();
                    services.AddHostedService<ServiceController>();
                })
                .Build();

            await host.RunAsync();
        }
    }
}
