﻿using JKOPay.BatchSystem.Core.Models.Option.ArgoWorkflow;
using JKOPay.BatchSystem.Core.Services;
using JKOPay.BatchSystem.Core.Utilities;
using JKOPay.BatchSystem.Job.Models;
using JKOPay.BatchSystem.Job.Models.Job;
using JKOPay.Platform.BatchSystem;
using JKOPay.Platform.BatchSystem.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace JKOPay.BatchSystem.Job
{
    public static class Program
    {
        public static string SlackToken = "";
        static async Task Main(string[] args)
        {
            try
            {
                BatchSystemPlatformExtension.Logger.Information("args: {Args}", string.Join(",", args));
                foreach (var arg in args)
                {
                    BatchSystemPlatformExtension.Logger.Information("Job arg item: {Arg}", arg);
                }

                var env = Environment.GetEnvironmentVariable("NETCORE_ENVIRONMENT");
                SlackToken = Environment.GetEnvironmentVariable($"SLACK_TOKEN_SECRET") ?? string.Empty;
                var host = Host.CreateDefaultBuilder(args)
                    .SetBatchPlatform(args)
                    .ConfigureHostConfiguration(config =>
                    {
                        config.Sources.Clear();
                        config.AddEnvironmentVariables();
                        config.AddYamlFile("appsettings.yaml", false, false);
                        config.AddYamlFile($"appsettings.{env}.yaml", false, false);
                    })
                    .ConfigureServices((context, services) =>
                    {
                        services.AddHttpClient("test", client =>
                        {
                            client.DefaultRequestHeaders.Add("X-Correlation-Id", Guid.NewGuid().ToString());
                        });

                        services.AddOption<ArgoWorkflowOption>("ArgoWorkflow");
                        services.AddScoped<IArgoWorkflowService, ArgoWorkflowService>();
                        services.AddScoped<IKafkaUtility, MockKafkaUtility>();
                        services.AddScoped<IArgoWorkflowUtility, ArgoWorkflowUtility>();
                        services.AddScoped<JsonNetSerializerUtility>();
                        services.AddScoped<IJobService, JobService>();
                        services.AddKeyedSingleton<string>("ENV", (_, _) =>
                        {
                            var tmp = Environment.GetEnvironmentVariable("NETCORE_ENVIRONMENT")?.ToLower();
                            var prefix =  tmp switch
                            {
                                "development" => "dev",
                                "sit" => "sit",
                                "uat" => "uat",
                                "prod" => "prod",
                                _ => "dev"
                            };

                            return prefix;
                        });

                        // serviceKey 請使用小寫
                        services.AddKeyedScoped<IJob, UpdateStatusJob>(serviceKey: "updatestatusjob");
                        services.AddKeyedScoped<IJob, DeleteCanceledWorkflowJob>(serviceKey: "deletecanceledworkflowjob");
                        services.AddKeyedScoped<IJob, SlackJob>(serviceKey: "slackjob");
                    })
                    .Build();

                var job = host.GetJob();
                await job.ExecuteAsync();
            }
            catch (Exception e)
            {
                BatchSystemPlatformExtension.Logger.Error(e.Message, e);
                throw;
            }

            BatchSystemPlatformExtension.Logger.Information("Job Completed.");
        }
    }
}