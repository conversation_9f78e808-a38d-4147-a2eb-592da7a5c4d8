builder.Host.UseSerilog((ctx, service, lc) =>
{
    var logConfig = lc.ReadFrom.Configuration(ctx.Configuration)
        .Filter.ByExcluding("RequestPath = '/metrics'")
        .Filter.ByExcluding("RequestPath = '/healthz'")
        .Filter.ByExcluding("RequestPath = '/health/live'")
        .Filter.ByExcluding("RequestPath = '/healthz/readiness'")
        .Filter.ByExcluding("RequestPath = '/healthz/liveness'");

    // 根據環境調整 EF Core 日誌過濾等級
    switch (env)
    {
        case "local" or "development":
            // 在開發環境只過濾 Information 等級的 EF Core 日誌，保留 Warning 以上
            logConfig.Filter.ByExcluding("SourceContext = 'Microsoft.EntityFrameworkCore.Database.Command' and @l = 'Information'");
            break;
        case "sit" or "uat" or "prod":
            // 在正式環境過濾 Warning 等級以下的 EF Core 日誌，只保留 Error 以上
            logConfig.Filter.ByExcluding("(SourceContext = 'Microsoft.EntityFrameworkCore.Database.Command' or SourceContext = 'Microsoft.EntityFrameworkCore.Query') and (@l = 'Information' or @l = 'Warning')");
            break;
    }

    logConfig.AddJsonDestructuringPolicies()
        .AddDefaultEnricher(service);

    if (env == "local")
    {
        Log.Information("在本地環境中配置 OpenTelemetry 日誌匯出...");
        logConfig.WriteTo.OpenTelemetry(options =>
        {
            // options.Endpoint = "https://gcp-operator-alloy.jkopay.app/";
            options.Endpoint = "http://10.4.19.11:30317";
            options.Protocol = OtlpProtocol.Grpc;
            options.ResourceAttributes = new Dictionary<string, object>
            {
                ["service.name"] = "BatchSystem.Api.Serilog",
                ["service.namespace"] = "JKOPay",
                ["service.instance.id"] = "BatchSystem.Api.Serilog-1.0.0",
                ["service.version"] = "1.0.0",
                ["service.environment"] = "local",
                ["index"] = 10,
                ["flag"] = true,
                ["value"] = 3.14,
                ["log.type"] = "serilog",
            };

            options.IncludedData =
                IncludedData.SpanIdField
                | IncludedData.TraceIdField
                | IncludedData.MessageTemplateRenderingsAttribute
                | IncludedData.SpecRequiredResourceAttributes
                | IncludedData.SourceContextAttribute;

            options.Headers = new Dictionary<string, string>
            {
                {"X-Scope-OrgId", "foo"}
            };

            options.BatchingOptions.BatchSizeLimit = 10;
            options.BatchingOptions.BufferingTimeLimit = TimeSpan.FromSeconds(1);
            options.BatchingOptions.QueueLimit = 5;

        });
    }
});
