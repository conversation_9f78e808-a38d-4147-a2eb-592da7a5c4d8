using System.Reflection;
using System.Text.RegularExpressions;
using Asp.Versioning;
using JKOPay.BatchSystem.Api.Extensions;
using JKOPay.BatchSystem.Api.Models;
using JKOPay.BatchSystem.Api.Models.Filters;
using JKOPay.BatchSystem.Api.Models.HealthChecks;
using JKOPay.BatchSystem.Core.Extensions.JsonConverter;
using JKOPay.BatchSystem.Core.Models.Option.ArgoWorkflow;
using JKOPay.BatchSystem.Core.Services;
using JKOPay.BatchSystem.Core.Utilities;
using JKOPay.Platform.BatchSystem.Models;
using JKOPay.Platform.BatchSystem.Models.Database;
using JKOPay.Platform.HttpClientExtension;
using JKOPay.Platform.HttpClientExtension.Models;
using JKOPay.Platform.LoggingExtension;
using JKOPay.Platform.OpenTelemetry;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using OpenTelemetry.Exporter;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Serilog;
using Serilog.HttpClient;
using Serilog.HttpClient.Extensions;
using Serilog.Sinks.OpenTelemetry;
using Swashbuckle.AspNetCore.Filters;

var builder = WebApplication.CreateBuilder(args);


// 獲取環境變數
var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLower();

var unlogPath = new List<string>()
{
    "/",
    "/api/v1/version"
};

// 載入配置文件
builder.Configuration.Sources.Clear();
builder.Configuration.AddEnvironmentVariables()
    .AddYamlFile("metrics.yaml", true)
    .AddYamlFile("appsettings.yaml", false)
    .AddYamlFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLower()}.yaml", false);

switch (env)
{
    case "local" or "development" or "sit":
        {
            var otlpHost = builder.Configuration["OpenTelemetry:Exporter:Otlp:Host"];
            var port = builder.Configuration["OpenTelemetry:Exporter:Otlp:Port"];
            builder.Services.AddOpentelemetry(
              serviceName: "JKOPay.BatchSystem.Api.Test",
              endPoint: $"{otlpHost}:{port}",
              // endPoint: "https://gcp-operator-alloy.jkopay.app:4317",
              // endPoint: "http://sit-sre-alloy-idc.monitoring:4317",
              protocol: OtlpExportProtocol.Grpc,
              configTracing: providerBuilder =>
              {
                  providerBuilder.AddEntityFrameworkCoreInstrumentation();

                  // 定義要排除的路徑集合
                  // 使用編譯期產生的正則表達式（GeneratedRegex）以提升效能
                  var excludedPathsRegex = new[]
                  {
                      GeneratedRegexes.Health(),
                      GeneratedRegexes.HealthzLiveness(),
                      GeneratedRegexes.HealthzReadiness(),
                      GeneratedRegexes.Metrics(),
                      GeneratedRegexes.Swagger()
                  };

                  providerBuilder.AddAspNetCoreInstrumentation(options =>
          {
              options.Filter = httpContext =>
        {
            // 檢查當前請求路徑是否在排除列表中
            var path = httpContext.Request.Path.Value;
            return !excludedPathsRegex.Any(regex => path != null && regex.IsMatch(path));
        };
          });
              },
              configMetrics: providerBuilder =>
              {
                  providerBuilder.SetExemplarFilter(ExemplarFilterType.AlwaysOn);
                  providerBuilder.AddPrometheusExporter();
              },
              meterNames: "BatchSystem.Metrics"
            );

            builder.Services.AddCustomMetrics<CustomMetricsProvider>();
            break;
        }
    case "uat" or "prod":
        builder.Services.AddOpenTelemetry()
          .ConfigureResource((resource => resource.AddService("JKOPay.BatchSystem.Api")))
          .WithMetrics(metrics =>
          {
              metrics.AddMeter("BatchSystem.Metrics");
              metrics.AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation();
              metrics.AddPrometheusExporter();
          });

        builder.Services.AddCustomMetrics<CustomMetricsProvider>();
        break;
}

builder.Services.AddHttpContextAccessor();
builder.Services.AddOption<ArgoWorkflowOption>("ArgoWorkflow");
builder.Services.AddScoped<IJobService, JobService>();
builder.Services.AddScoped<IScheduleJobService, ScheduleJobService>();
builder.Services.AddScoped<IWorkflowService, WorkflowService>();
builder.Services.AddScoped<IKafkaUtility, KafkaUtility>();
builder.Services.AddScoped<IArgoWorkflowService, ArgoWorkflowService>();
builder.Services.AddScoped<IArgoWorkflowUtility, ArgoWorkflowUtility>();
builder.Services.AddScoped<JsonNetSerializerUtility, JsonNetSerializerUtility>();
builder.Services.AddKeyedSingleton<string>("ENV", (_, _) =>
{
    var tmp = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLower();
    return tmp switch
    {
        "local" => "dev",
        "development" => "dev",
        "sit" => "sit",
        "uat" => "uat",
        "prod" => "prod",
        _ => "dev"
    };
});

builder.Services.AddCustomHeaderPropagation(options => options.Headers.Add("CustomHeader", "CustomHeader"));
builder.Services.RegisterHttpClients("Argo", options =>
    {
        options.LogMode = LogMode.LogAll;
        options.RequestHeaderLogMode = LogMode.LogAll;
        options.RequestBodyLogMode = LogMode.LogAll;
        options.RequestBodyLogTextLengthLimit = 5000;
        options.ResponseHeaderLogMode = LogMode.LogAll;
        options.ResponseBodyLogMode = LogMode.LogAll;
        options.ResponseBodyLogTextLengthLimit = 30000;
        options.MaskFormat = "*****";
        options.MaskedProperties.Clear();
    }, _ => { /* no-op */ })
    .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
    {
        ServerCertificateCustomValidationCallback =
            HttpClientHandler.DangerousAcceptAnyServerCertificateValidator
    });

builder.Services.RegisterHttpClientsWithConditionalLogging("ArgoWorkflow",
    configureOptions: options =>
    {
        // 一般日誌模式設為總是記錄（用於記錄基本請求資訊）
        options.GeneralExtendedLogMode = ExtendedLogMode.Always;

        // 請求相關的日誌設定
        options.RequestHeaderExtendedLogMode = ExtendedLogMode.OnError;
        options.RequestBodyExtendedLogMode = ExtendedLogMode.ConditionalLogging;

        // 請求和回應的 body 以及回應標頭都使用相同的條件式日誌記錄邏輯
        var requestResponseLoggingCondition =
            new ConditionalLoggingOptions
            {
                CustomCondition = (request, response, exception) =>
                {
                    // 如果有異常，總是記錄
                    if (exception != null) return true;
                    if (response == null) return true;

                    // 排除特定的 path 不紀錄 log
                    if (unlogPath.Any(p => p == request.RequestUri?.AbsolutePath)) return false;

                    // 當狀態碼不為 200 時
                    return response.StatusCode != System.Net.HttpStatusCode.OK;
                }
            };

        // 請求 body 使用條件式日誌記錄
        options.RequestBodyConditionalOptions = requestResponseLoggingCondition;

        // 回應標頭使用條件式日誌記錄
        options.ResponseHeaderExtendedLogMode = ExtendedLogMode.OnError;
        options.ResponseHeaderConditionalOptions = requestResponseLoggingCondition;

        // 回應 body 使用相同的條件式日誌記錄
        options.ResponseBodyExtendedLogMode = ExtendedLogMode.ConditionalLogging;
        options.ResponseBodyConditionalOptions = requestResponseLoggingCondition;
    },
    configureClient: httpClient =>
    {
        httpClient.DefaultRequestHeaders.Add("X-Correlation-Id", Guid.NewGuid().ToString());
        httpClient.Timeout = TimeSpan.FromSeconds(30);
    },
    configurePrimaryHandler: () => new HttpClientHandler
    {
        ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator
    },
    forwardDefaultHeaders: false);

builder.Services.AddApiVersioning(options =>
{
    options.DefaultApiVersion = new ApiVersion(1);
    options.ReportApiVersions = true;
    options.AssumeDefaultVersionWhenUnspecified = true;
    options.ApiVersionReader = ApiVersionReader.Combine(
        new UrlSegmentApiVersionReader(),
        new HeaderApiVersionReader("X-Api-Version"));
}).AddApiExplorer(options =>
{
    options.GroupNameFormat = "'v'V";
    options.SubstituteApiVersionInUrl = true;
});

// Add services to the container.
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(options =>
{
    options.ExampleFilters();
    // API 服務簡介
    // 讀取 XML 檔案產生 API 說明
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    options.IncludeXmlComments(xmlPath);
});
builder.Services.AddSwaggerExamplesFromAssemblies(Assembly.GetEntryAssembly());

builder.Services.AddHealthChecks().AddCheck<LivenessHealthCheck>("liveness_check", tags: ["liveness"]);
builder.Services.AddHealthChecks().AddCheck<ReadinessHealthCheck>("readiness_check", tags: ["readiness"]);
builder.Services.AddHealthChecks().AddDbContextCheck<BatchDbContext>(tags: ["mysql"]);
builder.Services.AddControllers(options =>
{
    options.Filters.Add<GlobalErrorCounterFilter>();
}).AddNewtonsoftJson(options =>
{
    options.SerializerSettings.Converters.Add(new JobPriorityEnumConverter());
});

builder.Services.AddKafkaProducer();
builder.Services.AddControllersWithViews();
builder.Services.AddCustomHeaderPropagation(options => options.Headers.Add("x-correlation-id"));
builder.Services.AddCors(options => options.AddDefaultPolicy(policyBuilder => policyBuilder.WithOrigins("*")));

var connectionString = builder.Configuration["DB"];
builder.Services.AddDbContextPool<BatchDbContext>(options =>
        {
            options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
            if (env == "local" || env == "development")
            {
                options.EnableSensitiveDataLogging();
            }
        });

builder.Host.UseSerilog((ctx, service, lc) =>
{
    lc.ReadFrom.Configuration(ctx.Configuration)
        .Filter.ByExcluding("RequestPath = '/metrics'")
        .Filter.ByExcluding("RequestPath = '/healthz'")
        .Filter.ByExcluding("RequestPath = '/health/live'")
        .Filter.ByExcluding("RequestPath = '/healthz/readiness'")
        .Filter.ByExcluding("RequestPath = '/healthz/liveness'")
        .AddJsonDestructuringPolicies()
        .AddDefaultEnricher(service);
    if (env == "local" || env == "development")
    {
        Log.Information("在本地環境中配置 OpenTelemetry 日誌匯出...");
        lc.WriteTo.OpenTelemetry(options =>
        {
            // options.Endpoint = "https://gcp-operator-alloy.jkopay.app/";
            options.Endpoint = "http://**********:30317";
            options.Protocol = OtlpProtocol.Grpc;
            options.ResourceAttributes = new Dictionary<string, object>
            {
                ["service.name"] = "BatchSystem.Api.Serilog",
                ["service.namespace"] = "JKOPay",
                ["service.instance.id"] = "BatchSystem.Api.Serilog-1.0.0",
                ["service.version"] = "1.0.0",
                ["service.environment"] = "local",
                ["index"] = 10,
                ["flag"] = true,
                ["value"] = 3.14,
                ["log.type"] = "serilog",
            };

            options.IncludedData =
                IncludedData.SpanIdField
                | IncludedData.TraceIdField
                | IncludedData.MessageTemplateRenderingsAttribute
                | IncludedData.SpecRequiredResourceAttributes
                | IncludedData.SourceContextAttribute;

            options.Headers = new Dictionary<string, string>
            {
                {"X-Scope-OrgId", "foo"}
            };

            options.BatchingOptions.BatchSizeLimit = 10;
            options.BatchingOptions.BufferingTimeLimit = TimeSpan.FromSeconds(1);
            options.BatchingOptions.QueueLimit = 5;

        });
    }
});

var app = builder.Build().UseStructuredLogger();
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.MapHealthChecks("/healthz/readiness", new HealthCheckOptions
{
    Predicate = (check) => check.Tags.Intersect(["mysql", "readiness"]).Any(),
    ResponseWriter = async (context, report) =>
    {
        context.Response.ContentType = "application/json";
        var healthCheck = new
        {
            status = report.Status.ToString(),
            checks = report.Entries.Select(p => new
            {
                name = p.Key,
                status = Enum.GetName(p.Value.Status),
                exception = p.Value.Exception?.ToString(),
                data = p.Value.Data
            })
        };
        var result = JsonConvert.SerializeObject(healthCheck);
        await context.Response.WriteAsync(result);
    }
});

app.MapHealthChecks("/healthz/liveness", new HealthCheckOptions
{
    Predicate = (check) => check.Tags.Intersect(["liveness"]).Any(), // 只執行標記為"custom"的健康檢查
    ResponseWriter = async (context, report) =>
    {
        context.Response.ContentType = "application/json";
        var healthCheck = new
        {
            status = report.Status.ToString(),
            checks = report.Entries.Select(p => new
            {
                name = p.Key,
                status = Enum.GetName(p.Value.Status),
                exception = p.Value.Exception?.ToString(),
                data = p.Value.Data
            })
        };
        var result = JsonConvert.SerializeObject(healthCheck);
        await context.Response.WriteAsync(result);
    }
});

app.MapHealthChecks("/healthz", new HealthCheckOptions
{
    Predicate = _ => true // 使用預設健康檢查
});

app.MapHealthChecks("/health/live", new HealthCheckOptions
{
    Predicate = _ => true // 使用預設健康檢查
});

app.MapControllers();
app.UseHeaderPropagation();
app.UseCors();
app.UseMiddleware<AdminPanelMiddleware>();
app.UseOpenTelemetryPrometheusScrapingEndpoint();

Log.Information("目前執行環境: {Environment}", env);
Log.Information("Kafka User: {KafkaUser}", Environment.GetEnvironmentVariable("KAFKA_USERNAME"));

var pw = Environment.GetEnvironmentVariable("KAFKA_PASSWORD");
// 遮罩密碼字串中間三個字母
var tmp = pw?.Substring(0, 3) + new string('*', pw.Length - 6) + pw.Substring(pw.Length - 3);
Log.Information($"Kafka Password: {tmp}");
Log.Information("===== 應用程式啟動完成，開始接受請求 =====");
app.Run();

internal static partial class GeneratedRegexes
{
    [GeneratedRegex("^/health", RegexOptions.Compiled | RegexOptions.CultureInvariant)]
    public static partial Regex Health();

    [GeneratedRegex("^/healthz/liveness", RegexOptions.Compiled | RegexOptions.CultureInvariant)]
    public static partial Regex HealthzLiveness();

    [GeneratedRegex("^/healthz/readiness", RegexOptions.Compiled | RegexOptions.CultureInvariant)]
    public static partial Regex HealthzReadiness();

    [GeneratedRegex("^/metrics", RegexOptions.Compiled | RegexOptions.CultureInvariant)]
    public static partial Regex Metrics();

    [GeneratedRegex("^/swagger", RegexOptions.Compiled | RegexOptions.CultureInvariant)]
    public static partial Regex Swagger();
}
