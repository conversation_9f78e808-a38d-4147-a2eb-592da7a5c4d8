using Asp.Versioning;
using JKOPay.BatchSystem.Api.Models;
using JKOPay.BatchSystem.Core.Models;
using JKOPay.BatchSystem.Core.Models.Api;
using Microsoft.AspNetCore.Mvc;

namespace JKOPay.BatchSystem.Api.Controllers;

/// <summary>
/// For Api test.
/// </summary>
[ApiController]
[ApiVersion(1)]
[Route("api/v{v:apiVersion}/test")]
public class TestController
{
    private readonly ILogger<TestController> _logger;

    /// <summary>
    /// TestController
    /// </summary>
    /// <param name="logger"></param>
    public TestController(ILogger<TestController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Test non json result
    /// </summary>
    /// <returns></returns>
    [HttpGet("NonJsonResult")]
    public async Task<string> NonJsonResult()
    {
        return await Task.FromResult<string>("username: jamis");
    }

    /// <summary>
    /// Test request log
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("RequestLog")]
    public async Task<object> RequestLog(TestRequest request)
    {
        _logger.LogInformation("In Test Controller RequestLog, {Name}", request.Name);
        return await Task.FromResult<object>(new Dictionary<string, object>
        {
            { "Name", request.Name },
            { "Age", request.Age },
            { "Token", request.Token }
        });
    }

    /// <summary>
    /// Test error result
    /// </summary>
    /// <returns></returns>
    [HttpGet("ErrorResult")]
    public Task<ResponseModel<object>> ErrorResponse()
    {
        return Task.FromResult(new ResponseModel<object>()
        {
            ResultCode = ResultCode.ScheduleJobEx0002,
            ResultObject = new Dictionary<string, object?>
            {
                { "Name", "Jamis" },
                { "Age", 18 }
            }
        });
    }
}
