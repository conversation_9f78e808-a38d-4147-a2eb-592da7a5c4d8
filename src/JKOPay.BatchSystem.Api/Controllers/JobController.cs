// ReS<PERSON>per disable once ConvertToPrimaryConstructor
// Re<PERSON><PERSON><PERSON> disable once ConvertToPrimaryConstructor
using Asp.Versioning;
using JKOPay.BatchSystem.Core.Models.Api;
using JKOPay.BatchSystem.Core.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace JKOPay.BatchSystem.Api.Controllers;

/// <summary>
/// All about job action
/// </summary>
[ApiController]
[ApiVersion(1)]
[Route("api/v{v:apiVersion}/job")]
public class JobController(IJobService jobService, ILogger<JobController> logger)
{
    private readonly IJobService _jobService = jobService;

    private readonly ILogger<JobController> _logger = logger;

    /// <summary>
    /// 取得 Job 資訊
    /// </summary>
    /// <param name="jobId">Job Id</param>
    /// <param name="workflowId">Workflow Id</param>
    /// <returns>回傳 Job 資訊</returns>
    [HttpGet("GetJob")]
    public async Task<ResponseModel<object>> GetJob([FromQuery] string? jobId, [FromQuery] string workflowId)
    {
        if (jobId != default)
        {
            return await _jobService.GetJobAsyncUseJobId(jobId);
        }
        else
        {
            return await _jobService.GetJobAsyncUseWorkflowId(workflowId);
        }
    }

    /// <summary>
    /// 取得 Job 資訊
    /// </summary>
    /// <param name="jobId">Job Id</param>
    /// <returns>回傳 Job 詳細資訊</returns>
    [HttpGet("info/{jobId}")]
    public async Task<ResponseModel<object>> GetJobInfo(string jobId)
    {
        return await _jobService.GetJobInfoAsync(jobId);
    }

    /// <summary>
    /// 取得 Job 列表
    /// </summary>
    /// <param name="team">團隊名稱</param>
    /// <param name="page">Page</param>
    /// <param name="count">單頁顯示筆數</param>
    /// <returns>回傳 Job 列表</returns>
    [HttpGet("list/{team}/{page}")]
    public async Task<object> GetList(string team, int page, [FromQuery] int count)
    {
        return await _jobService.GetJobsAsync(team, page, count);
    }

    /// <summary>
    /// 取得 Job 列表 (管理後台使用)
    /// </summary>
    /// <param name="team">團隊名稱</param>
    /// <param name="jobId">JobId</param>
    /// <param name="currentPage">Page</param>
    /// <param name="pageSize">單頁顯示筆數</param>
    /// <returns>回傳 Job 列表或指定 Job 資訊</returns>
    [HttpGet("list/{team}/adminpanel")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<object> GetList(string team, [FromQuery] string? jobId, [FromQuery] int currentPage, [FromQuery] int pageSize)
    {
        if (jobId != default)
        {
            var response = await _jobService.GetJobAsyncUseJobId(jobId);
            return new ResponseModel<object>
            {
                Result = "0001",
                ResultObject = new[] { response.ResultObject },
                Message = "Success"
            };
        }
        else
        {
           return await _jobService.GetJobsAsync(team, currentPage, pageSize);
        }
    }

    /// <summary>
    /// 建立 Job
    /// </summary>
    /// <param name="request">Job 相關資訊</param>
    /// <remarks>
    /// Sample request:
    ///
    ///     POST /create
    ///     {
    ///         "Priority": "[Immediate, Priority, Normal]",
    ///         "JobName": "{Your Job Name}",
    ///         "WorkflowTemplateName": "{Your Workflow Template Name}",
    ///         "TemplateName": "main",
    ///         "KafkaHeader": { },
    ///         "Message": { "jobData" : {Your a parameter object} },
    ///         "ServiceNamespace": "{Your team name}"
    ///     }
    ///
    /// </remarks>
    [HttpPost("execute")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<Dictionary<string, string>>>(200)]
    public async Task<ResponseModel<Dictionary<string, string>>> CreateJob(CreateJob request)
    {
        return await _jobService.CreateJobAsync(request);
    }

    /// <summary>
    /// Get Job Info Template
    /// </summary>
    /// <param name="namespace">命名空間</param>
    /// <param name="templateName">範本名稱</param>
    /// <param name="jobId">作業 ID</param>
    /// <param name="source">來源</param>
    /// <returns>回傳作業資訊範本</returns>
    [HttpGet("info/template/{namespace}/{templateName}/{jobId}/{source}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<Dictionary<string, string>>>(200)]
    public async Task<ResponseModel<object>> GetInfoTemplate(string @namespace, string templateName, string jobId, string source)
    {
        var response = await _jobService.GetJobInfoTemplateAsync(@namespace, jobId, templateName, source);
        return response;
    }

    /// <summary>
    /// 重新執行 Job
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="jobId">Job id</param>
    /// <returns>回傳重新執行結果</returns>
    [HttpPut("resubmit/{namespace}/{jobId}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<object>>(200)]
    public async Task<ResponseModel<object>> ResubmitJob(string @namespace, string jobId)
    {
        return await _jobService.UpdateJobAsync(@namespace, jobId);
    }

    /// <summary>
    /// 建立 Schedule job 的 job record
    /// </summary>
    /// <param name="request">Schedule workflow 相關資訊</param>
    /// <remarks>建立 schedule job 的 job record</remarks>
    /// <returns>回傳建立結果</returns>
    [HttpPost("create/schedule")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<object>>(200)]
    public async Task<ResponseModel<object>> CreateJob(CreateScheduleJob request)
    {
        return await _jobService.CreateJobAsync(request);
    }

    /// <summary>
    /// 取消 Job
    /// </summary>
    /// <param name="namespace">團隊使用的 k8s 的空間名稱</param>
    /// <param name="jobId">建立 Job 時回傳的 Job ID</param>
    /// <returns>回傳取消結果</returns>
    [HttpPost("cancel/{namespace}/{jobId}")]
    [Produces("application/json")]
    [ProducesResponseType<ResponseModel<object>>(200)]
    public async Task<ResponseModel<object>> CancelJob(string @namespace, string jobId)
    {
        return await _jobService.CanceledJobAsync(@namespace, jobId);
    }

    /// <summary>
    /// 更新 Job Record 中的狀態
    /// </summary>
    /// <param name="request">更新狀態請求資料</param>
    /// <returns>回傳更新結果</returns>
    [HttpPost("update/status")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<Dictionary<string, string>>> UpdateJobStatus(UpdateJob request)
    {
        _logger.LogDebug("Update data: {Data}", JsonConvert.SerializeObject(request));
        return await _jobService.UpdateJobStatusAsync(request);
    }

    /// <summary>
    /// 更新 Job Record 中的需要處理資料筆數
    /// </summary>
    /// <param name="totalCount">總資料筆數</param>
    /// <param name="workflowId">工作流程 ID</param>
    /// <returns>回傳更新結果</returns>
    [HttpPost("update/totalRecords")]
    // [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<object>> UpdateTotalRecords(int totalCount, string workflowId)
    {
        return await _jobService.UpdateTotalRecordsAsync(totalCount, workflowId);
    }

    /// <summary>
    /// 更新 Job Record 中的已處理資料筆數
    /// </summary>
    /// <param name="processedRecords">已處理資料筆數</param>
    /// <param name="workflowId">工作流程 ID</param>
    /// <returns>回傳更新結果</returns>
    [HttpPost("update/processedRecords")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<ResponseModel<object>> UpdateProcessRecords(int processedRecords, string workflowId)
    {
        return await _jobService.UpdateProcessRecordsAsync(processedRecords, workflowId);
    }
}
